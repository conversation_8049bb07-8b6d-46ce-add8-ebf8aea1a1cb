import { io } from 'socket.io-client'

// Socket.IO客户端管理器
class SocketIOManager {
  constructor() {
    this.socket = null
    this.isConnected = false
    this.listeners = new Map()
    this.connectionListeners = new Set()
  }

  /**
   * 连接Socket.IO服务器
   */
  connect() {
    const socketUrl = 'http://localhost:8000'
    const startTime = performance.now()
    console.log('开始Socket.IO连接...')

    try {
      this.socket = io(socketUrl, {
        transports: ['websocket', 'polling'], // 支持WebSocket和轮询
        timeout: 20000, // 20秒连接超时
        reconnection: true, // 启用自动重连
        reconnectionAttempts: 5, // 最大重连次数
        reconnectionDelay: 3000, // 重连延迟3秒
      })

      this.socket.on('connect', () => {
        const endTime = performance.now()
        console.log(
          `Socket.IO连接已建立，耗时: ${(endTime - startTime).toFixed(2)}ms`
        )
        this.isConnected = true
        this.notifyConnectionChange(true)
      })

      this.socket.on('disconnect', (reason) => {
        console.log('Socket.IO连接已断开，原因:', reason)
        this.isConnected = false
        this.notifyConnectionChange(false)
      })

      this.socket.on('connect_error', (error) => {
        console.error('Socket.IO连接错误:', error)
        this.isConnected = false
        this.notifyConnectionChange(false)
      })

      // 处理连接确认消息
      this.socket.on('connected', () => {
        console.log('Socket.IO连接确认')
      })

      // 设置通用消息处理器
      this.setupMessageHandlers()
    } catch (error) {
      console.error('创建Socket.IO连接失败:', error)
    }
  }

  /**
   * 设置消息处理器
   */
  setupMessageHandlers() {
    // 直接监听我们需要的事件
    this.socket.on('status_update', (...args) => {
      this.handleMessage('status_update', ...args)
    })

    this.socket.on('results_update', (...args) => {
      this.handleMessage('results_update', ...args)
    })
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(event, ...args) {
    const listeners = this.listeners.get(event) || []

    listeners.forEach((listener) => {
      try {
        listener(...args)
      } catch (error) {
        console.error(`Socket.IO消息处理错误 [${event}]:`, error)
      }
    })
  }

  /**
   * 断开Socket.IO连接
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.isConnected = false
    this.notifyConnectionChange(false)
  }

  /**
   * 发送事件并等待确认（emit-acknowledge模式）
   */
  async emitWithAck(event, data, timeout = 10000) {
    if (!this.socket || !this.isConnected) {
      throw new Error('Socket.IO未连接')
    }

    try {
      const response = await this.socket
        .timeout(timeout)
        .emitWithAck(event, data)
      return response
    } catch (error) {
      console.error(`Socket.IO emit-ack错误 [${event}]:`, error)
      throw error
    }
  }

  /**
   * 发送事件（不等待确认）
   */
  emit(event, data) {
    if (!this.socket || !this.isConnected) {
      console.warn('Socket.IO未连接，无法发送事件:', event)
      return
    }

    this.socket.emit(event, data)
  }

  /**
   * 添加事件监听器
   */
  addListener(event, listener) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(listener)
  }

  /**
   * 移除事件监听器
   */
  removeListener(event, listener) {
    const listeners = this.listeners.get(event)
    if (listeners) {
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 添加连接状态监听器
   */
  addConnectionListener(listener) {
    this.connectionListeners.add(listener)
  }

  /**
   * 移除连接状态监听器
   */
  removeConnectionListener(listener) {
    this.connectionListeners.delete(listener)
  }

  /**
   * 通知连接状态变化
   */
  notifyConnectionChange(isConnected) {
    this.connectionListeners.forEach((listener) => {
      try {
        listener(isConnected)
      } catch (error) {
        console.error('连接状态监听器错误:', error)
      }
    })
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return this.isConnected
  }

  /**
   * 获取Socket实例（用于高级用法）
   */
  getSocket() {
    return this.socket
  }
}

// 创建全局Socket.IO管理器实例
const socketManager = new SocketIOManager()

// 导出管理器实例
export default socketManager
