import { useState, useEffect } from 'react'
import { apiService, wsManager } from '../utils/api'
import BacktestConfigRow from './BacktestConfigRow'
import BacktestStatus from './BacktestStatus'
import ResultsDisplay from './ResultsDisplay'

function BacktestDashboard() {
  const [strategies, setStrategies] = useState([])
  const [strategiesLoading, setStrategiesLoading] = useState(true)
  const [selectedStrategy, setSelectedStrategy] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [backtestStatus, setBacktestStatus] = useState({
    is_running: false,
    current_strategy: null,
    start_date: null,
    end_date: null,
    progress: 0,
    status: 'idle',
    error_message: null,
    results: null,
  })
  const [results, setResults] = useState(null)
  const [error, setError] = useState('')

  useEffect(() => {
    // 设置WebSocket状态更新监听器（优先执行，不阻塞）
    const handleStatusUpdate = (message) => {
      if (message.type === 'status_update') {
        const status = message.data
        setBacktestStatus(status)

        // 根据状态更新结果和错误信息
        if (status.status === 'completed' && status.results) {
          setResults(status.results)
          setError('')
        } else if (status.status === 'error' && status.error_message) {
          setError(status.error_message)
          setResults(null)
        } else if (status.status === 'running') {
          setError('')
        }
      } else if (message.type === 'results_update') {
        // 处理单独的结果数据更新
        setResults(message.data)
        setError('')
      }
    }

    // 添加WebSocket监听器（立即执行）
    wsManager.addListener('status_update', handleStatusUpdate)

    // 异步加载策略列表（不阻塞WebSocket）
    loadStrategies()

    return () => {
      // 清理WebSocket监听器
      wsManager.removeListener('status_update', handleStatusUpdate)
    }
  }, [])

  // 加载策略列表
  const loadStrategies = async () => {
    try {
      setStrategiesLoading(true)
      const strategiesList = await apiService.getStrategies()
      setStrategies(strategiesList || [])
    } catch (error) {
      console.error('获取策略列表失败:', error)
      setError(error.message)
    } finally {
      setStrategiesLoading(false)
    }
  }

  const handleStartBacktest = async () => {
    if (!selectedStrategy || !startDate || !endDate) {
      setError('请选择策略和日期范围')
      return
    }

    if (new Date(startDate) >= new Date(endDate)) {
      setError('开始日期必须早于结束日期')
      return
    }

    setError('')
    setResults(null)

    try {
      const response = await apiService.startBacktest({
        strategy: selectedStrategy,
        start_date: startDate,
        end_date: endDate,
      })

      console.log('回测已开始:', response)

      // 回测状态将通过WebSocket实时更新，无需轮询
    } catch (error) {
      console.error('启动回测失败:', error)
      setError(error.message)
    }
  }

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <div className="alert alert-error">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{error}</span>
        </div>
      )}

      {/* 回测配置区域 */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">回测配置</h2>

          <BacktestConfigRow
            strategies={strategies}
            selectedStrategy={selectedStrategy}
            onStrategyChange={setSelectedStrategy}
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onStartBacktest={handleStartBacktest}
            isRunning={backtestStatus.is_running}
            disabled={backtestStatus.is_running || strategiesLoading}
          />
        </div>
      </div>

      {/* 回测状态 */}
      {(backtestStatus.is_running || backtestStatus.current_strategy) && (
        <BacktestStatus status={backtestStatus} />
      )}

      {/* 结果展示 */}
      {results && <ResultsDisplay results={results} />}
    </div>
  )
}

export default BacktestDashboard
