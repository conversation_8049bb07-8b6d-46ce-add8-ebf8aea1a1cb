import { useState } from 'react'
import { socket } from '../utils/socket'
import BacktestConfigRow from './BacktestConfigRow'
import BacktestStatus from './BacktestStatus'
import ResultsDisplay from './ResultsDisplay'

function BacktestDashboard({ strategies, backtestStatus, isConnected }) {
  const [selectedStrategy, setSelectedStrategy] = useState('')
  const [startDate, setStartDate] = useState('')
  const [endDate, setEndDate] = useState('')
  const [error, setError] = useState('')

  const handleStartBacktest = async () => {
    if (!selectedStrategy || !startDate || !endDate) {
      setError('请选择策略和日期范围')
      return
    }

    if (new Date(startDate) >= new Date(endDate)) {
      setError('开始日期必须早于结束日期')
      return
    }

    socket.emit(
      'start_backtest',
      {
        strategy: selectedStrategy,
        start_date: startDate,
        end_date: endDate,
      },
      (response) => {
        if (response.error) {
          console.error('启动回测失败:', response.error)
          setError(response.error)
        } else {
          console.log(response.message)
          const task_id = response.task_id
          console.log('任务ID:', task_id)
        }
      }
    )
  }

  return (
    <div className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <div className="alert alert-error">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="stroke-current shrink-0 h-6 w-6"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{error}</span>
        </div>
      )}

      {/* 回测配置区域 */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">回测配置</h2>

          <BacktestConfigRow
            strategies={strategies}
            selectedStrategy={selectedStrategy}
            onStrategyChange={setSelectedStrategy}
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onStartBacktest={handleStartBacktest}
            isRunning={backtestStatus.is_running}
            disabled={backtestStatus.is_running || !isConnected}
          />
        </div>
      </div>

      {/* 回测状态 */}
      {(backtestStatus.is_running || backtestStatus.current_strategy) && (
        <BacktestStatus status={backtestStatus} />
      )}

      {/* 结果展示 */}
      {backtestStatus.results && (
        <ResultsDisplay results={backtestStatus.results} />
      )}
    </div>
  )
}

export default BacktestDashboard
