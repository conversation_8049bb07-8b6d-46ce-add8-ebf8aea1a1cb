import socketManager from './socket.js'

// Socket.IO API服务类
class SocketIOApiService {
  constructor() {
    this.socketManager = socketManager
  }

  /**
   * 确保Socket.IO连接已建立
   */
  async ensureConnection() {
    if (!this.socketManager.getConnectionStatus()) {
      throw new Error('Socket.IO连接未建立')
    }
  }

  /**
   * 获取策略列表
   */
  async getStrategies() {
    try {
      await this.ensureConnection()
      
      const response = await this.socketManager.emitWithAck('get_strategies', {})
      
      if (response.error) {
        throw new Error(response.error)
      }
      
      return response.strategies || []
    } catch (error) {
      throw new Error(
        `获取策略列表失败: ${error.message}`
      )
    }
  }

  /**
   * 开始回测
   * @param {Object} params - 回测参数
   * @param {string} params.strategy - 策略名称
   * @param {string} params.start_date - 开始日期 (YYYY-MM-DD)
   * @param {string} params.end_date - 结束日期 (YYYY-MM-DD)
   */
  async startBacktest(params) {
    try {
      await this.ensureConnection()
      
      const response = await this.socketManager.emitWithAck('start_backtest', params)
      
      if (response.error) {
        if (response.error.includes('已有回测任务在运行中')) {
          throw new Error('已有回测任务在运行中')
        } else if (response.error.includes('未找到策略文件')) {
          throw new Error(`未找到策略文件: ${params.strategy}`)
        } else {
          throw new Error(response.error)
        }
      }
      
      return response
    } catch (error) {
      throw new Error(
        `启动回测失败: ${error.message}`
      )
    }
  }

  /**
   * 添加状态更新监听器
   */
  addStatusUpdateListener(listener) {
    this.socketManager.addListener('status_update', listener)
  }

  /**
   * 移除状态更新监听器
   */
  removeStatusUpdateListener(listener) {
    this.socketManager.removeListener('status_update', listener)
  }

  /**
   * 添加结果更新监听器
   */
  addResultsUpdateListener(listener) {
    this.socketManager.addListener('results_update', listener)
  }

  /**
   * 移除结果更新监听器
   */
  removeResultsUpdateListener(listener) {
    this.socketManager.removeListener('results_update', listener)
  }

  /**
   * 添加连接状态监听器
   */
  addConnectionListener(listener) {
    this.socketManager.addConnectionListener(listener)
  }

  /**
   * 移除连接状态监听器
   */
  removeConnectionListener(listener) {
    this.socketManager.removeConnectionListener(listener)
  }

  /**
   * 连接到Socket.IO服务器
   */
  connect() {
    this.socketManager.connect()
  }

  /**
   * 断开Socket.IO连接
   */
  disconnect() {
    this.socketManager.disconnect()
  }

  /**
   * 获取连接状态
   */
  isConnected() {
    return this.socketManager.getConnectionStatus()
  }
}

// 创建服务实例
const socketIOApiService = new SocketIOApiService()

// 导出服务
export { socketIOApiService, SocketIOApiService }

// 默认导出主要服务
export default socketIOApiService
