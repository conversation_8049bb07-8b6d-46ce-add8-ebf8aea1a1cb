{"name": "vite-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@mantine/core": "^8.1.3", "@mantine/dates": "^8.1.3", "@mantine/hooks": "^8.1.3", "axios": "^1.10.0", "dayjs": "^1.11.13", "highcharts": "^12.3.0", "highcharts-react-official": "^3.2.2", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "daisyui": "^5.0.46", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "postcss-preset-mantine": "^1.18.0", "postcss-simple-vars": "^7.0.1", "tailwindcss": "^4.1.11", "vite": "^7.0.0"}}