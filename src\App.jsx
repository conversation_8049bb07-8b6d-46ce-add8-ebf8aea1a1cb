import { useState, useEffect } from 'react'
import { Wifi, WifiOff } from 'lucide-react'
import socketIOApiService from './utils/socketio-api'
import BacktestDashboard from './components/BacktestDashboard'
import '@mantine/core/styles.css'
import '@mantine/dates/styles.css'
import { MantineProvider } from '@mantine/core'

function App() {
  const [connected, setConnected] = useState(false)

  useEffect(() => {
    // 设置连接状态监听器
    const handleConnectionChange = (isConnected) => {
      console.log(isConnected ? 'Socket.IO已连接' : 'Socket.IO连接断开')
      setConnected(isConnected)
    }

    // 添加连接状态监听器
    socketIOApiService.addConnectionListener(handleConnectionChange)

    // 连接Socket.IO
    socketIOApiService.connect()

    return () => {
      // 清理连接监听器
      socketIOApiService.removeConnectionListener(handleConnectionChange)
      socketIOApiService.disconnect()
    }
  }, [])

  return (
    <MantineProvider>
      <div className="min-h-screen bg-base-200">
        <div className="navbar bg-base-100 shadow-lg">
          <div className="flex-1">
            <a className="btn btn-ghost text-xl">QuantBack 量化回测系统</a>
          </div>
          <div className="flex-none">
            <div
              className={`badge ${
                connected ? 'badge-success' : 'badge-error'
              } flex items-center gap-1`}
            >
              {connected ? <Wifi size={16} /> : <WifiOff size={16} />}
              {connected ? '已连接' : '未连接'}
            </div>
          </div>
        </div>

        <div className="container mx-auto p-4">
          <BacktestDashboard />
        </div>
      </div>
    </MantineProvider>
  )
}

export default App
