import { useState, useEffect, useRef } from 'react'
import { Wifi, WifiOff } from 'lucide-react'
import { socket } from './utils/socket'
import BacktestDashboard from './components/BacktestDashboard'
import '@mantine/core/styles.css'
import '@mantine/dates/styles.css'
import { MantineProvider } from '@mantine/core'

function App() {
  const initialized = useRef(false)
  const [connected, setConnected] = useState(socket.connected)
  const [strategies, setStrategies] = useState([])
  const [backtestStatus, setBacktestStatus] = useState({
    is_running: false,
    current_strategy: null,
    start_date: null,
    end_date: null,
    progress: 0,
    status: 'idle',
    error_message: null,
    task_id: null,
    results: null,
  })

  useEffect(() => {
    function onConnect() {
      setConnected(true)
      initialized.current = true
      console.log('Socket.IO已连接')
      socket.emit('get_strategies', (response) => {
        if (response.strategies) {
          console.log('策略列表:', response.strategies)
          setStrategies(response.strategies)
        }
      })
    }

    function onDisconnect() {
      setConnected(false)
      console.log('Socket.IO连接断开')
    }

    function onStatusUpdate(status) {
      setBacktestStatus(status)
    }

    function onResultsUpdate(results) {
      setBacktestStatus((prev) => ({ ...prev, results }))
    }

    // 注册事件监听器
    socket.on('connect', onConnect)
    socket.on('disconnect', onDisconnect)
    socket.on('status_update', onStatusUpdate)
    socket.on('results_update', onResultsUpdate)

    return () => {
      socket.off('connect', onConnect)
      socket.off('disconnect', onDisconnect)
      socket.off('status_update', onStatusUpdate)
      socket.off('results_update', onResultsUpdate)
    }
  }, [])

  return (
    <MantineProvider>
      <div className="min-h-screen bg-base-200">
        <div className="navbar bg-base-100 shadow-lg">
          <div className="flex-1">
            <a className="btn btn-ghost text-xl">QuantAI 量化回测系统</a>
          </div>
          <div className="flex-none">
            <div
              className={`badge ${
                connected ? 'badge-success' : 'badge-error'
              } flex items-center gap-1`}
            >
              {connected ? <Wifi size={16} /> : <WifiOff size={16} />}
              {connected ? '已连接' : '未连接'}
            </div>
          </div>
        </div>

        <div className="container mx-auto p-4">
          {initialized.current ? (
            <BacktestDashboard
              strategies={strategies}
              backtestStatus={backtestStatus}
              isConnected={connected}
            />
          ) : (
            <div className="flex flex-col items-center justify-center min-h-[200px] gap-4">
              <span className="loading loading-spinner loading-lg text-primary"></span>
              <div className="text-lg font-medium text-gray-600">
                正在连接到服务器...
              </div>
            </div>
          )}
        </div>
      </div>
    </MantineProvider>
  )
}

export default App
