#!/usr/bin/env python3
"""
量化回测Socket.IO服务器
"""

import asyncio
import logging
import uuid
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

import aiofiles
import aiofiles.os
import socketio
import uvicorn

from quantback.log_setup import setup_logging
from quantback.port_bt.engine import BacktestEngine

# 设置日志
logger = logging.getLogger(__name__)

# 创建Socket.IO服务器
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins='*',  # 在生产环境中应该限制具体域名
    logger=logger,
    engineio_logger=True,
)
app = socketio.ASGIApp(sio)


# 线程池用于执行耗时的回测任务
executor = ThreadPoolExecutor(max_workers=2)


# 全局状态管理
class BacktestState:
    def __init__(self):
        self.is_running = False
        self.current_strategy = None
        self.start_date = None
        self.end_date = None
        self.progress = 0
        self.status = 'idle'  # idle, running, completed, error
        self.error_message = None
        self.results = None
        self.task_id = None

    async def update_status(self, **kwargs):
        """更新状态并广播给所有Socket.IO连接"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

        # 广播状态更新
        status_message = {
            'is_running': self.is_running,
            'current_strategy': self.current_strategy,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'progress': self.progress,
            'status': self.status,
            'error_message': self.error_message,
            'results': self.results,
            'task_id': self.task_id,
        }
        await sio.emit('status_update', status_message)

    def to_dict(self, include_results=True):
        """转换为字典格式"""
        data = {
            'is_running': self.is_running,
            'current_strategy': self.current_strategy,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'progress': self.progress,
            'status': self.status,
            'error_message': self.error_message,
            'task_id': self.task_id,
        }

        # 可选择是否包含结果数据（用于优化连接速度）
        if include_results:
            data['results'] = self.results
        else:
            data['results'] = None

        return data


backtest_state = BacktestState()


async def get_strategy_files() -> List[Dict[str, str]]:
    """获取策略文件列表（异步版本）"""
    strategies_dir = Path(__file__).parent / 'port_bt' / 'strategies'
    strategy_files = []

    # 使用aiofiles.os检查目录是否存在
    if await aiofiles.os.path.exists(strategies_dir):
        # 使用aiofiles.os.listdir异步获取目录内容
        try:
            files = await aiofiles.os.listdir(strategies_dir)
            for filename in files:
                if filename.endswith('.py') and filename != '__init__.py':
                    file_path = strategies_dir / filename
                    strategy_files.append(
                        {'filename': filename, 'name': Path(filename).stem, 'path': str(file_path)}
                    )
        except OSError as e:
            logger.error(f'读取策略目录失败: {e}')

    return strategy_files


def load_strategy_module(strategy_path: str):
    """动态加载策略模块"""
    import importlib.util

    spec = importlib.util.spec_from_file_location('strategy', strategy_path)
    if spec is None or spec.loader is None:
        raise ValueError(f'无法加载策略文件: {strategy_path}')

    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    return module


def run_backtest_sync(strategy_path: str, start_date: str, end_date: str, task_id: str):
    """同步运行回测（在线程池中执行）"""
    global backtest_state

    try:
        # 加载策略模块
        strategy_module = load_strategy_module(strategy_path)

        if not hasattr(strategy_module, 'initialize'):
            raise ValueError('策略文件必须包含initialize函数')

        if not hasattr(strategy_module, 'STRATEGY_CONFIG'):
            raise ValueError('策略文件必须包含STRATEGY_CONFIG配置')

        # 获取策略配置并更新日期
        strategy_config = strategy_module.STRATEGY_CONFIG.copy()
        strategy_config['start_date'] = start_date
        strategy_config['end_date'] = end_date

        # 创建回测引擎
        engine = BacktestEngine(
            initialize_func=strategy_module.initialize, strategy_config=strategy_config
        )

        # 运行回测
        results = engine.run()

        # 处理结果数据，转换为可序列化的格式
        processed_results = process_backtest_results(results)

        # 更新结果到状态（同步更新，异步广播由包装器处理）
        backtest_state.results = processed_results

        logger.info(f'回测完成: {task_id}')
        return processed_results

    except Exception as e:
        logger.error(f'回测执行错误: {e}')
        raise e


def process_backtest_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """处理回测结果，转换为前端可用的格式"""
    try:
        processed = {}

        # 处理投资组合历史数据
        if 'portfolio_history' in results:
            portfolio_df = results['portfolio_history']

            # 处理日期索引 - 检查索引类型
            if hasattr(portfolio_df.index, 'strftime'):
                # 如果是DatetimeIndex
                dates = portfolio_df.index.strftime('%Y-%m-%d').tolist()
            else:
                # 如果是其他类型的索引，尝试转换为字符串
                dates = [str(date) for date in portfolio_df.index]

            processed['portfolio_history'] = {
                'dates': dates,
                'total_value': portfolio_df['total_value'].tolist(),
                'available_cash': portfolio_df['available_cash'].tolist(),
                'returns': portfolio_df['returns'].fillna(0).tolist(),
                'cumulative_returns': (portfolio_df['returns'].fillna(0).cumsum()).tolist(),
            }

        # 处理性能统计
        if 'performance_stats' in results:
            processed['performance_stats'] = results['performance_stats']

        # 处理交易记录
        if 'trade_records' in results:
            trade_records = results['trade_records']
            processed['trade_records'] = []
            for record in trade_records:
                # 处理时间字段
                timestamp = record.get('timestamp')
                if timestamp and hasattr(timestamp, 'strftime'):
                    # 如果是datetime对象，格式化为日期字符串
                    date_str = timestamp.strftime('%Y-%m-%d')
                    datetime_str = timestamp.isoformat()
                elif timestamp and hasattr(timestamp, 'date'):
                    # 如果有date方法，提取日期部分
                    date_str = timestamp.date().strftime('%Y-%m-%d')
                    datetime_str = str(timestamp)
                elif timestamp:
                    # 尝试解析字符串格式的时间戳
                    try:
                        from datetime import datetime

                        if 'T' in str(timestamp):
                            # ISO格式时间戳
                            dt = datetime.fromisoformat(str(timestamp).replace('Z', '+00:00'))
                            date_str = dt.strftime('%Y-%m-%d')
                            datetime_str = str(timestamp)
                        else:
                            date_str = str(timestamp)
                            datetime_str = str(timestamp)
                    except:
                        date_str = str(timestamp)
                        datetime_str = str(timestamp)
                else:
                    date_str = ''
                    datetime_str = ''

                # 处理买卖方向
                side = record.get('side', '')
                action = '买入' if side == 'buy' else '卖出' if side == 'sell' else side

                # 计算交易金额
                volume = record.get('volume', 0)
                price = record.get('price', 0)
                amount = volume * price

                processed['trade_records'].append(
                    {
                        'date': date_str,  # 前端期望的字段名是date，格式为YYYY-MM-DD
                        'datetime': datetime_str,  # 保留datetime字段以兼容其他可能的用途
                        'symbol': record.get('symbol', ''),
                        'action': action,
                        'volume': volume,
                        'price': price,
                        'amount': amount,
                        'commission': record.get('commission', 0),
                        'tax': 0,  # 当前系统中税费单独计算，这里设为0
                    }
                )

        # 处理最终持仓
        if 'final_positions' in results:
            positions = results['final_positions']
            processed['final_positions'] = [
                {
                    'symbol': symbol,
                    'volume': pos.total_volume,
                    'available_volume': pos.closeable_volume,
                    'avg_cost': pos.avg_cost,
                    'market_value': pos.value,
                }
                for symbol, pos in positions.items()
                if pos.total_volume > 0
            ]

        return processed

    except Exception as e:
        logger.error(f'处理回测结果错误: {e}')
        return {'error': str(e)}


async def run_backtest_in_background(
    strategy_path: str, start_date: str, end_date: str, task_id: str
):
    """在后台运行回测"""
    loop = asyncio.get_event_loop()
    try:
        # 广播开始状态
        await backtest_state.update_status(
            is_running=True, progress=0, status='running', error_message=None
        )

        # 在线程池中执行回测
        await loop.run_in_executor(
            executor, run_backtest_sync, strategy_path, start_date, end_date, task_id
        )

        # 广播完成状态
        await backtest_state.update_status(is_running=False, progress=100, status='completed')

    except Exception as e:
        logger.error(f'后台回测任务执行失败: {e}')
        # 广播错误状态
        await backtest_state.update_status(is_running=False, status='error', error_message=str(e))


# Socket.IO事件处理器
async def send_initial_state(sid):
    """异步发送初始状态，不阻塞连接"""
    try:
        # 发送当前状态（不包含大量结果数据）
        await sio.emit('status_update', backtest_state.to_dict(include_results=False), room=sid)

        # 如果有结果数据，单独发送
        if backtest_state.results:
            await sio.emit('results_update', backtest_state.results, room=sid)
    except Exception as e:
        logger.error(f'发送初始状态失败: {e}')


@sio.event
async def connect(sid, environ=None, auth=None):
    """客户端连接事件"""
    logger.info(f'Socket.IO client connected: {sid}')
    # 异步发送状态，此处应尽快返回, 不要阻塞连接确认, 否则会导致连接超时.
    asyncio.create_task(send_initial_state(sid))


@sio.event
async def disconnect(sid):
    """客户端断开连接事件"""
    logger.info(f'Socket.IO client disconnected: {sid}')


@sio.event
async def get_strategies(sid):
    """获取策略列表"""
    try:
        # 使用aiofiles异步获取策略文件列表
        strategies = await get_strategy_files()
        return {'strategies': strategies}
    except Exception as e:
        logger.error(f'获取策略列表错误: {e}')
        return {'error': f'获取策略列表失败: {str(e)}'}


@sio.event
async def start_backtest(sid, data):
    """开始回测"""
    global backtest_state

    try:
        # 检查是否已有回测在运行
        if backtest_state.is_running:
            return {'error': '已有回测任务在运行中'}

        # 验证请求数据
        if not data or not all(key in data for key in ['strategy', 'start_date', 'end_date']):
            return {'error': '缺少必要参数'}

        strategy = data['strategy']
        start_date = data['start_date']
        end_date = data['end_date']

        # 查找策略文件
        strategies = get_strategy_files()
        strategy_file = None
        for strat in strategies:
            if strat['name'] == strategy:
                strategy_file = strat['path']
                break

        if not strategy_file:
            return {'error': f'未找到策略文件: {strategy}'}

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 更新状态
        backtest_state.current_strategy = strategy
        backtest_state.start_date = start_date
        backtest_state.end_date = end_date
        backtest_state.task_id = task_id
        backtest_state.results = None
        backtest_state.error_message = None

        # 在后台执行回测
        asyncio.create_task(
            run_backtest_in_background(strategy_file, start_date, end_date, task_id)
        )

        return {'message': '回测已开始', 'task_id': task_id}

    except Exception as e:
        logger.error(f'启动回测错误: {e}')
        return {'error': f'启动回测失败: {str(e)}'}


if __name__ == '__main__':
    # 设置日志
    setup_logging()

    print('=' * 60)
    print('QuantBack Socket.IO 回测服务器')
    print('=' * 60)
    print(f'启动时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    print('=' * 60)

    # 启动服务器, 同时监听IPv4和IPv6
    uvicorn.run(app, host='', port=8000, log_level='info')
